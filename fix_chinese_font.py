#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字体修复脚本
解决matplotlib中文显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import pandas as pd
import numpy as np

def check_available_fonts():
    """检查系统可用字体"""
    print("🔍 正在检查系统可用字体...")
    
    # 获取所有字体
    fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 查找中文字体
    chinese_fonts = []
    chinese_keywords = ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'KaiTi', 'FangSong', 'STSong', 'STHeiti', 'STKaiti']
    
    for font in fonts:
        for keyword in chinese_keywords:
            if keyword in font:
                chinese_fonts.append(font)
                break
    
    print(f"📋 找到的中文字体: {len(chinese_fonts)} 个")
    for font in chinese_fonts[:10]:  # 显示前10个
        print(f"   - {font}")
    
    if len(chinese_fonts) > 10:
        print(f"   ... 还有 {len(chinese_fonts) - 10} 个字体")
    
    return chinese_fonts

def setup_chinese_font():
    """设置中文字体"""
    print("\n🔧 正在设置中文字体...")
    
    # 优先级字体列表
    priority_fonts = [
        'Microsoft YaHei',  # 微软雅黑
        'SimHei',          # 黑体
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'Microsoft YaHei UI',
        'STHeiti',         # 华文黑体
        'STSong',          # 华文宋体
    ]
    
    # 获取系统字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 查找可用的中文字体
    selected_font = None
    for font in priority_fonts:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font]
        plt.rcParams['axes.unicode_minus'] = False
        print(f"✅ 成功设置字体: {selected_font}")
        return selected_font
    else:
        print("❌ 未找到合适的中文字体")
        # 尝试使用备用方案
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        return None

def test_chinese_display():
    """测试中文显示效果"""
    print("\n🧪 正在测试中文显示效果...")
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试数据
    categories = ['北京', '上海', '广州', '深圳', '杭州']
    values = [100, 85, 75, 90, 80]
    
    # 创建柱状图
    bars = ax.bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
    
    # 设置标题和标签
    ax.set_title('中文字体测试图表', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('城市', fontsize=12, fontweight='bold')
    ax.set_ylabel('数值', fontsize=12, fontweight='bold')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{value}', ha='center', va='bottom', fontsize=10)
    
    # 设置网格
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, max(values) * 1.1)
    
    # 保存测试图表
    plt.tight_layout()
    plt.savefig('chinese_font_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 测试图表已生成: chinese_font_test.png")

def install_chinese_font_windows():
    """Windows系统字体安装指导"""
    print("\n💡 Windows系统中文字体安装指导:")
    print("1. 如果中文显示为方框，请尝试以下方法:")
    print("   - 确保系统已安装中文字体（通常Windows都有）")
    print("   - 重启Python环境")
    print("   - 清除matplotlib缓存:")
    print("     import matplotlib")
    print("     matplotlib.get_cachedir()")
    print("     # 删除该目录下的所有文件")
    print("\n2. 手动下载字体:")
    print("   - 下载思源黑体: https://github.com/adobe-fonts/source-han-sans")
    print("   - 下载微软雅黑字体文件")
    print("   - 将字体文件放到系统字体目录")

def main():
    """主函数"""
    print("🚀 开始修复中文字体显示问题...")
    
    # 检查可用字体
    chinese_fonts = check_available_fonts()
    
    # 设置中文字体
    selected_font = setup_chinese_font()
    
    # 测试中文显示
    test_chinese_display()
    
    if selected_font:
        print(f"\n🎉 中文字体设置成功！使用字体: {selected_font}")
        print("现在可以重新运行 visualization_project.py")
    else:
        print("\n⚠️ 中文字体设置可能有问题")
        install_chinese_font_windows()
    
    # 显示当前字体设置
    print(f"\n📋 当前matplotlib字体设置:")
    print(f"   font.sans-serif: {plt.rcParams['font.sans-serif']}")
    print(f"   axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")

if __name__ == "__main__":
    main()
