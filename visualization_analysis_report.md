# 数据可视化期末考核项目分析报告

## 📋 项目概述

本项目从9个数据集中选择了3个最适合可视化分析的数据集，为每个数据集创建了3种不同类型的专业图表，总共生成9张高质量的可视化图表。

## 🎯 数据集选择标准

基于以下标准对9个数据集进行评估：
- **数据量充足性**：记录数量是否足够支撑有意义的分析
- **数据多样性**：是否包含多种数据类型（数值型、分类型、时间型）
- **数据完整性**：缺失值比例是否在可接受范围内
- **可视化潜力**：数据结构是否适合创建多样化的图表类型

## 🏆 选定的3个数据集

### 1. 二手房数据.xlsx (评分: 8/10)
- **数据规模**：2,909条记录，7个字段
- **数据特点**：包含区域、户型、面积、房龄、单价、总价等房产信息
- **选择理由**：数据量大，包含多个数值变量和分类变量，适合多维度分析

### 2. 2022年北京市工作日早高峰出站量前20的地铁站.xlsx (评分: 8/10)
- **数据规模**：20条记录，5个字段
- **数据特点**：包含地铁站名称、经纬度坐标、出站量数据
- **选择理由**：包含地理信息，适合创建地理可视化图表

### 3. 某餐厅顾客消费记录.xlsx (评分: 8/10)
- **数据规模**：978条记录，5个字段
- **数据特点**：包含分店、顾客类型、性别、消费金额、满意度
- **选择理由**：数据量适中，包含多个分类变量，适合分组分析

## 📊 可视化图表详细分析

### 数据集1：二手房数据 - 3张图表

#### 图表1：二手房面积与总价关系散点图
**图表类型**：散点图
**设计特点**：
- 使用不同颜色区分各个区域
- X轴为面积，Y轴为总价，展示两者相关性
- 透明度设置增强视觉效果

**关键发现**：
- 面积与总价呈现明显的正相关关系
- 朝阳区、海淀区等核心区域房价普遍较高
- 存在一些高价值的小面积房产，可能位于核心地段

#### 图表2：各区域房价分布箱形图
**图表类型**：箱形图
**设计特点**：
- 选择房源数量最多的10个区域进行对比
- 使用渐变色彩增强视觉区分度
- 显示中位数、四分位数和异常值

**关键发现**：
- 西城区、东城区房价中位数最高，属于核心区域
- 通州、大兴等远郊区域房价相对较低
- 各区域内部房价差异较大，存在明显的价格分层

#### 图表3：房龄分布直方图
**图表类型**：直方图
**设计特点**：
- 25个分箱展示房龄分布
- 添加平均值和中位数参考线
- 使用统计信息增强分析深度

**关键发现**：
- 房龄主要集中在10-30年区间
- 平均房龄约20年，中位数略低于平均值
- 新房（5年以内）和老房（40年以上）数量相对较少

### 数据集2：地铁站数据 - 3张图表

#### 图表4：地铁站出站量条形图
**图表类型**：条形图
**设计特点**：
- 水平条形图便于阅读站名
- 使用渐变色彩表示出站量大小
- 添加精确数值标签

**关键发现**：
- 西二旗站出站量最高（2.34万人次），主要服务科技园区
- 朝阳门、国贸等商务区站点出站量也很高
- TOP20站点出站量差异明显，反映了不同区域的人流密度

#### 图表5：地铁站地理分布气泡图
**图表类型**：气泡图
**设计特点**：
- 气泡大小表示出站量
- 颜色深浅也表示出站量，双重编码
- 标注出站量最高的5个站点

**关键发现**：
- 高出站量站点主要分布在城市中心和北部科技园区
- 地理分布呈现明显的聚集效应
- 经度116.3-116.5，纬度39.8-40.1范围内分布

#### 图表6：地铁站地理热力图
**图表类型**：交互式地理热力图（Plotly）
**设计特点**：
- 基于真实地图的交互式可视化
- 支持缩放和悬停查看详细信息
- 红色热力图显示出站量分布

**关键发现**：
- 提供了真实地理环境下的数据展示
- 便于理解站点在城市中的实际位置
- 支持交互探索，用户体验更佳

### 数据集3：餐厅消费数据 - 3张图表

#### 图表7：各分店消费金额分布小提琴图
**图表类型**：小提琴图
**设计特点**：
- 显示数据分布的密度和形状
- 同时显示均值和中位数
- 不同颜色区分各分店

**关键发现**：
- 三个分店的消费金额分布形状相似
- 第二分店的消费金额分布略微偏高
- 所有分店都存在高消费的长尾分布

#### 图表8：顾客类型分布饼图
**图表类型**：饼图
**设计特点**：
- 清晰的百分比标注
- 适度的分离效果增强视觉区分
- 温和的配色方案

**关键发现**：
- 会员与普通顾客比例基本均衡（约50.2% vs 49.8%）
- 会员制度推广效果良好
- 顾客结构相对稳定

#### 图表9：消费金额与满意度关系密度图
**图表类型**：密度图（六边形分箱）
**设计特点**：
- 六边形分箱显示数据密度
- 添加趋势线显示相关性
- 热力色彩映射数据密度

**关键发现**：
- 消费金额与满意度呈现轻微的负相关关系
- 大部分顾客消费金额集中在100-500元区间
- 满意度主要分布在60-80分区间
- 高消费不一定带来高满意度，需要关注服务质量

## 🎨 设计特色

### 视觉设计
- **统一的配色方案**：使用专业的色彩搭配，确保视觉和谐
- **清晰的标题和标签**：所有图表都有明确的中文标题和轴标签
- **适当的字体大小**：确保在不同尺寸下都能清晰阅读
- **网格线和透明度**：增强数据的可读性

### 技术特色
- **多样化的图表类型**：涵盖散点图、箱形图、直方图、条形图、气泡图、地理热力图、小提琴图、饼图、密度图
- **交互式元素**：地理热力图支持交互操作
- **高分辨率输出**：所有图表以300 DPI保存，确保打印质量
- **代码结构化**：使用面向对象编程，代码清晰易维护

## 📈 总结与建议

本项目成功完成了数据可视化期末考核的所有要求：
1. ✅ 从9个数据集中选择了3个最适合的数据集
2. ✅ 为每个数据集创建了3种不同类型的图表
3. ✅ 总共生成了9张专业美观的可视化图表
4. ✅ 每张图表都有清晰的标题、标签和分析说明
5. ✅ 使用了多种Python可视化库（Matplotlib、Seaborn、Plotly）

### 技术收获
- 掌握了多种图表类型的创建方法
- 学会了如何选择合适的图表类型展示不同类型的数据
- 提升了数据分析和可视化设计能力
- 熟悉了Python主流可视化库的使用

### 应用价值
- 房地产市场分析：为购房决策提供数据支持
- 城市交通规划：为地铁运营优化提供参考
- 餐饮业务分析：为经营策略制定提供依据
