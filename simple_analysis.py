import pandas as pd
import os

# 简单分析每个数据集
data_path = "数据可视化数据集-A"

files = [
    "2022年北京市各行政区常住人口.xlsx",
    "2022年北京市工作日早高峰出站量前20的地铁站.xlsx", 
    "二手房数据.xlsx",
    "产品销售统计表.xlsx",
    "国内生产总值季度数据.xlsx",
    "某公司产品销售数据.xlsx",
    "某餐厅顾客消费记录.xlsx",
    "营销和产品销售表.xlsx"
]

for file in files:
    try:
        print(f"\n=== {file} ===")
        df = pd.read_excel(os.path.join(data_path, file))
        print(f"形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("前3行:")
        print(df.head(3))
        print("-" * 50)
    except Exception as e:
        print(f"错误: {e}")
