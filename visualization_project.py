#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化期末考核项目
作者：AI助手
日期：2024年

本项目从9个数据集中选择了3个最适合可视化的数据集，
为每个数据集创建3种不同类型的专业图表，总共9张图表。
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
import warnings
from pathlib import Path
import matplotlib.font_manager as fm

# 设置中文字体和样式
def setup_chinese_font():
    """设置中文字体"""
    # 尝试多种中文字体
    chinese_fonts = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'FangSong',        # 仿宋
        'STSong',          # 华文宋体
        'STKaiti',         # 华文楷体
        'STHeiti',         # 华文黑体
        'DejaVu Sans'      # 备用字体
    ]

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # 找到第一个可用的中文字体
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break

    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font]
        print(f"✅ 使用字体: {selected_font}")
    else:
        # 如果没有找到中文字体，尝试下载并使用
        print("⚠️ 未找到中文字体，尝试使用系统默认字体")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']

    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    return selected_font

# 设置字体
setup_chinese_font()
warnings.filterwarnings('ignore')

# 设置图表样式
sns.set_style("whitegrid")
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')

class DataVisualization:
    """数据可视化类"""

    def __init__(self):
        self.data_path = Path("数据可视化数据集-A")
        self.output_path = Path("visualization_outputs")
        self.output_path.mkdir(exist_ok=True)

    def load_data(self):
        """加载三个推荐的数据集"""
        print("🔄 正在加载数据集...")

        # 数据集1：二手房数据
        self.house_data = pd.read_excel(self.data_path / "二手房数据.xlsx")
        print(f"✅ 二手房数据加载完成：{self.house_data.shape}")

        # 数据集2：地铁站数据
        self.subway_data = pd.read_excel(self.data_path / "2022年北京市工作日早高峰出站量前20的地铁站.xlsx")
        print(f"✅ 地铁站数据加载完成：{self.subway_data.shape}")

        # 数据集3：餐厅消费数据
        self.restaurant_data = pd.read_excel(self.data_path / "某餐厅顾客消费记录.xlsx")
        print(f"✅ 餐厅消费数据加载完成：{self.restaurant_data.shape}")

    def create_house_visualizations(self):
        """创建二手房数据的3个可视化图表"""
        print("\n📊 正在创建二手房数据可视化...")

        # 图表1：散点图 - 面积与总价关系
        plt.figure(figsize=(12, 8))

        # 选择主要区域进行可视化
        main_districts = self.house_data['所在区'].value_counts().head(8).index
        filtered_data = self.house_data[self.house_data['所在区'].isin(main_districts)]

        colors = plt.cm.Set3(np.linspace(0, 1, len(main_districts)))

        for i, district in enumerate(main_districts):
            district_data = filtered_data[filtered_data['所在区'] == district]
            plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'],
                       alpha=0.6, s=50, label=district, color=colors[i])

        plt.xlabel('面积（平方米）', fontsize=12, fontweight='bold')
        plt.ylabel('总价（万元）', fontsize=12, fontweight='bold')
        plt.title('北京二手房面积与总价关系散点图\n（按区域分类）', fontsize=16, fontweight='bold', pad=20)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_path / '1_二手房面积总价散点图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 图表2：箱形图 - 不同区域房价分布
        plt.figure(figsize=(14, 8))

        # 选择房源数量较多的区域
        top_districts = self.house_data['所在区'].value_counts().head(10).index
        plot_data = self.house_data[self.house_data['所在区'].isin(top_districts)]

        box_plot = plt.boxplot([plot_data[plot_data['所在区'] == district]['单价（元/平方米）']
                               for district in top_districts],
                              labels=top_districts, patch_artist=True)

        # 设置箱形图颜色
        colors = plt.cm.viridis(np.linspace(0, 1, len(top_districts)))
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        plt.xlabel('区域', fontsize=12, fontweight='bold')
        plt.ylabel('单价（元/平方米）', fontsize=12, fontweight='bold')
        plt.title('北京各区域二手房单价分布箱形图', fontsize=16, fontweight='bold', pad=20)
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_path / '2_各区域房价分布箱形图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 图表3：直方图 - 房龄分布
        plt.figure(figsize=(12, 8))

        plt.hist(self.house_data['房龄（年）'], bins=25, alpha=0.7, color='skyblue',
                edgecolor='black', linewidth=0.5)

        # 添加统计信息
        mean_age = self.house_data['房龄（年）'].mean()
        median_age = self.house_data['房龄（年）'].median()

        plt.axvline(mean_age, color='red', linestyle='--', linewidth=2,
                   label=f'平均房龄: {mean_age:.1f}年')
        plt.axvline(median_age, color='orange', linestyle='--', linewidth=2,
                   label=f'中位数房龄: {median_age:.1f}年')

        plt.xlabel('房龄（年）', fontsize=12, fontweight='bold')
        plt.ylabel('房源数量', fontsize=12, fontweight='bold')
        plt.title('北京二手房房龄分布直方图', fontsize=16, fontweight='bold', pad=20)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_path / '3_房龄分布直方图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ 二手房数据可视化完成")

    def create_subway_visualizations(self):
        """创建地铁站数据的3个可视化图表"""
        print("\n🚇 正在创建地铁站数据可视化...")

        # 图表4：条形图 - TOP20地铁站出站量
        plt.figure(figsize=(14, 10))

        # 按出站量排序
        sorted_data = self.subway_data.sort_values('出站量（万人次）', ascending=True)

        bars = plt.barh(range(len(sorted_data)), sorted_data['出站量（万人次）'],
                       color=plt.cm.plasma(np.linspace(0, 1, len(sorted_data))))

        plt.yticks(range(len(sorted_data)), sorted_data['地铁站'])
        plt.xlabel('出站量（万人次）', fontsize=12, fontweight='bold')
        plt.ylabel('地铁站', fontsize=12, fontweight='bold')
        plt.title('2022年北京市工作日早高峰出站量TOP20地铁站', fontsize=16, fontweight='bold', pad=20)

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, sorted_data['出站量（万人次）'])):
            plt.text(value + 0.02, i, f'{value:.2f}', va='center', fontsize=10)

        plt.grid(True, alpha=0.3, axis='x')
        plt.tight_layout()
        plt.savefig(self.output_path / '4_地铁站出站量条形图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 图表5：气泡图 - 地理位置与出站量
        plt.figure(figsize=(12, 10))

        scatter = plt.scatter(self.subway_data['经度'], self.subway_data['纬度'],
                            s=self.subway_data['出站量（万人次）'] * 200,  # 气泡大小
                            c=self.subway_data['出站量（万人次）'],
                            cmap='YlOrRd', alpha=0.7, edgecolors='black', linewidth=1)

        plt.xlabel('经度', fontsize=12, fontweight='bold')
        plt.ylabel('纬度', fontsize=12, fontweight='bold')
        plt.title('北京地铁站地理分布气泡图\n（气泡大小表示出站量）', fontsize=16, fontweight='bold', pad=20)

        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('出站量（万人次）', fontsize=12, fontweight='bold')

        # 添加站点标签（只标注出站量最高的几个）
        top_stations = self.subway_data.nlargest(5, '出站量（万人次）')
        for _, station in top_stations.iterrows():
            plt.annotate(station['地铁站'],
                        (station['经度'], station['纬度']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=9, ha='left')

        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_path / '5_地铁站地理分布气泡图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 图表6：地理热力图（使用plotly）
        fig = px.scatter_mapbox(self.subway_data,
                               lat='纬度', lon='经度',
                               size='出站量（万人次）',
                               color='出站量（万人次）',
                               hover_name='地铁站',
                               hover_data={'出站量（万人次）': True},
                               color_continuous_scale='Reds',
                               size_max=30,
                               zoom=10,
                               title='北京地铁站出站量地理热力图')

        fig.update_layout(mapbox_style="open-street-map",
                         title_font_size=16,
                         title_x=0.5)

        fig.write_html(self.output_path / '6_地铁站地理热力图.html')
        fig.show()

        print("✅ 地铁站数据可视化完成")

    def create_restaurant_visualizations(self):
        """创建餐厅消费数据的3个可视化图表"""
        print("\n🍽️ 正在创建餐厅消费数据可视化...")

        # 图表7：小提琴图 - 不同分店的消费金额分布
        plt.figure(figsize=(12, 8))

        violin_parts = plt.violinplot([self.restaurant_data[self.restaurant_data['分店'] == store]['消费金额（元）']
                                     for store in self.restaurant_data['分店'].unique()],
                                    positions=range(1, len(self.restaurant_data['分店'].unique()) + 1),
                                    showmeans=True, showmedians=True)

        # 设置小提琴图颜色
        colors = ['lightblue', 'lightgreen', 'lightcoral']
        for i, pc in enumerate(violin_parts['bodies']):
            pc.set_facecolor(colors[i])
            pc.set_alpha(0.7)

        plt.xticks(range(1, len(self.restaurant_data['分店'].unique()) + 1),
                  self.restaurant_data['分店'].unique())
        plt.xlabel('分店', fontsize=12, fontweight='bold')
        plt.ylabel('消费金额（元）', fontsize=12, fontweight='bold')
        plt.title('各分店顾客消费金额分布小提琴图', fontsize=16, fontweight='bold', pad=20)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_path / '7_分店消费金额小提琴图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 图表8：饼图 - 顾客类型比例
        plt.figure(figsize=(10, 8))

        customer_counts = self.restaurant_data['顾客类型'].value_counts()
        colors = ['#ff9999', '#66b3ff']

        wedges, texts, autotexts = plt.pie(customer_counts.values,
                                          labels=customer_counts.index,
                                          autopct='%1.1f%%',
                                          colors=colors,
                                          startangle=90,
                                          explode=(0.05, 0.05))

        # 美化文字
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
            autotext.set_fontsize(12)

        for text in texts:
            text.set_fontsize(12)
            text.set_fontweight('bold')

        plt.title('餐厅顾客类型分布饼图', fontsize=16, fontweight='bold', pad=20)
        plt.axis('equal')
        plt.tight_layout()
        plt.savefig(self.output_path / '8_顾客类型分布饼图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 图表9：密度图 - 消费金额与满意度关系
        plt.figure(figsize=(12, 8))

        # 创建二维密度图
        plt.hexbin(self.restaurant_data['消费金额（元）'], self.restaurant_data['顾客满意度'],
                  gridsize=20, cmap='YlOrRd', alpha=0.8)

        plt.xlabel('消费金额（元）', fontsize=12, fontweight='bold')
        plt.ylabel('顾客满意度', fontsize=12, fontweight='bold')
        plt.title('顾客消费金额与满意度关系密度图', fontsize=16, fontweight='bold', pad=20)

        # 添加颜色条
        cbar = plt.colorbar()
        cbar.set_label('数据点密度', fontsize=12, fontweight='bold')

        # 添加趋势线
        z = np.polyfit(self.restaurant_data['消费金额（元）'], self.restaurant_data['顾客满意度'], 1)
        p = np.poly1d(z)
        plt.plot(self.restaurant_data['消费金额（元）'], p(self.restaurant_data['消费金额（元）']),
                "r--", alpha=0.8, linewidth=2, label=f'趋势线 (斜率: {z[0]:.3f})')

        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_path / '9_消费金额满意度密度图.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ 餐厅消费数据可视化完成")

if __name__ == "__main__":
    # 创建可视化对象
    viz = DataVisualization()

    # 加载数据
    viz.load_data()

    # 创建可视化图表
    viz.create_house_visualizations()
    viz.create_subway_visualizations()
    viz.create_restaurant_visualizations()

    print("\n🎉 所有9张可视化图表创建完成！")
    print(f"📁 图表保存在：{viz.output_path}")
    print("\n📊 图表清单：")
    print("1. 二手房面积总价散点图")
    print("2. 各区域房价分布箱形图")
    print("3. 房龄分布直方图")
    print("4. 地铁站出站量条形图")
    print("5. 地铁站地理分布气泡图")
    print("6. 地铁站地理热力图")
    print("7. 分店消费金额小提琴图")
    print("8. 顾客类型分布饼图")
    print("9. 消费金额满意度密度图")
