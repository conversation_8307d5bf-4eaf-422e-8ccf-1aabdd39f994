#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集分析脚本
用于分析9个数据集的结构和特征，选择最适合可视化的3个数据集
"""

import pandas as pd
import os
import numpy as np
from pathlib import Path

def analyze_dataset(file_path, file_name):
    """分析单个数据集"""
    try:
        print(f"\n{'='*60}")
        print(f"📊 分析数据集: {file_name}")
        print(f"{'='*60}")
        
        # 读取数据
        df = pd.read_excel(file_path)
        
        # 基本信息
        print(f"📈 基本信息:")
        print(f"   数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")
        print(f"   内存使用: {df.memory_usage(deep=True).sum() / 1024:.2f} KB")
        
        # 列信息
        print(f"\n📋 列信息:")
        for i, (col, dtype) in enumerate(df.dtypes.items(), 1):
            null_count = df[col].isnull().sum()
            null_pct = (null_count / len(df)) * 100
            print(f"   {i:2d}. {col:<25} | {str(dtype):<12} | 缺失值: {null_count:3d} ({null_pct:5.1f}%)")
        
        # 数值列统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"\n📊 数值列统计 ({len(numeric_cols)} 列):")
            for col in numeric_cols:
                stats = df[col].describe()
                print(f"   {col}: 最小值={stats['min']:.2f}, 最大值={stats['max']:.2f}, 均值={stats['mean']:.2f}")
        
        # 分类列统计
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            print(f"\n📝 分类列统计 ({len(categorical_cols)} 列):")
            for col in categorical_cols:
                unique_count = df[col].nunique()
                print(f"   {col}: {unique_count} 个唯一值")
                if unique_count <= 10:
                    top_values = df[col].value_counts().head(5)
                    print(f"      前5个值: {dict(top_values)}")
        
        # 时间列检测
        date_cols = []
        for col in df.columns:
            if df[col].dtype == 'object':
                try:
                    pd.to_datetime(df[col].dropna().head(10))
                    date_cols.append(col)
                except:
                    pass
        
        if date_cols:
            print(f"\n📅 可能的时间列: {date_cols}")
        
        # 数据预览
        print(f"\n👀 数据预览 (前3行):")
        print(df.head(3).to_string())
        
        # 可视化潜力评估
        print(f"\n🎯 可视化潜力评估:")
        score = 0
        reasons = []
        
        # 数据量评分
        if df.shape[0] >= 20:
            score += 2
            reasons.append("✅ 数据量充足")
        else:
            reasons.append("⚠️ 数据量较少")
        
        # 数值列评分
        if len(numeric_cols) >= 2:
            score += 3
            reasons.append("✅ 多个数值列，适合多维分析")
        elif len(numeric_cols) == 1:
            score += 2
            reasons.append("✅ 有数值列")
        else:
            reasons.append("⚠️ 缺少数值列")
        
        # 分类列评分
        if len(categorical_cols) >= 1:
            score += 2
            reasons.append("✅ 有分类列，适合分组分析")
        
        # 时间列评分
        if date_cols:
            score += 2
            reasons.append("✅ 有时间列，适合时间序列分析")
        
        # 数据完整性评分
        missing_pct = (df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100
        if missing_pct < 10:
            score += 1
            reasons.append("✅ 数据完整性好")
        else:
            reasons.append("⚠️ 缺失值较多")
        
        print(f"   评分: {score}/10")
        for reason in reasons:
            print(f"   {reason}")
        
        return {
            'file_name': file_name,
            'shape': df.shape,
            'numeric_cols': len(numeric_cols),
            'categorical_cols': len(categorical_cols),
            'date_cols': len(date_cols),
            'missing_pct': missing_pct,
            'score': score,
            'reasons': reasons,
            'columns': list(df.columns),
            'sample_data': df.head(3)
        }
        
    except Exception as e:
        print(f"❌ 分析 {file_name} 时出错: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始分析数据集...")
    
    # 数据集路径
    data_path = Path("数据可视化数据集-A")
    
    # 获取所有Excel文件
    excel_files = list(data_path.glob("*.xlsx"))
    
    if not excel_files:
        print("❌ 未找到Excel文件")
        return
    
    print(f"📁 找到 {len(excel_files)} 个数据集文件")
    
    # 分析所有数据集
    results = []
    for file_path in excel_files:
        result = analyze_dataset(file_path, file_path.name)
        if result:
            results.append(result)
    
    # 排序并推荐
    print(f"\n{'='*80}")
    print("🏆 数据集可视化潜力排名")
    print(f"{'='*80}")
    
    results.sort(key=lambda x: x['score'], reverse=True)
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['file_name']} (评分: {result['score']}/10)")
        print(f"   数据形状: {result['shape']}")
        print(f"   数值列: {result['numeric_cols']}, 分类列: {result['categorical_cols']}, 时间列: {result['date_cols']}")
        print(f"   缺失值比例: {result['missing_pct']:.1f}%")
    
    # 推荐前3个
    print(f"\n{'='*80}")
    print("🎯 推荐用于可视化的前3个数据集")
    print(f"{'='*80}")
    
    top_3 = results[:3]
    for i, result in enumerate(top_3, 1):
        print(f"\n推荐 {i}: {result['file_name']}")
        print(f"理由:")
        for reason in result['reasons']:
            print(f"  {reason}")

if __name__ == "__main__":
    main()
